# K230 触摸LCD程序切换菜单系统
# 基于Marcus架构设计，Ryan实现
# 支持触摸操作的程序管理界面

import time
import os
import sys
import gc
from machine import TOUCH
from media.display import *
from media.media import *
import image

# 菜单配置常量
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
BUTTON_WIDTH = 200
BUTTON_HEIGHT = 80
BUTTON_MARGIN = 20
TITLE_HEIGHT = 60

class TouchHandler:
    """触摸事件处理类"""
    
    def __init__(self):
        """初始化触摸设备"""
        try:
            self.tp = TOUCH(0)  # 初始化触摸设备0
            self.last_touch_time = 0
            self.touch_debounce = 200  # 防抖动时间(ms)
            print("✅ 触摸设备初始化成功")
        except Exception as e:
            print(f"❌ 触摸设备初始化失败: {e}")
            self.tp = None
    
    def get_touch_event(self):
        """获取触摸事件，包含防抖动处理"""
        if not self.tp:
            return None
            
        try:
            current_time = time.ticks_ms()
            # 防抖动处理
            if current_time - self.last_touch_time < self.touch_debounce:
                return None
                
            touch_data = self.tp.read()
            if touch_data and len(touch_data) > 0:
                touch_point = touch_data[0]
                
                # 只处理按下事件
                if touch_point.event == TOUCH.EVENT_DOWN:
                    self.last_touch_time = current_time
                    return {
                        'x': touch_point.x,
                        'y': touch_point.y,
                        'event': touch_point.event
                    }
        except Exception as e:
            print(f"触摸读取错误: {e}")
            
        return None

class MenuRenderer:
    """菜单界面渲染类"""
    
    def __init__(self, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT):
        """初始化渲染器"""
        self.width = width
        self.height = height
        self.img = image.Image(width, height, image.ARGB8888)
        
        # 颜色定义
        self.colors = {
            'background': (30, 30, 30),      # 深灰背景
            'title': (255, 255, 255),        # 白色标题
            'button_normal': (70, 130, 180), # 钢蓝色按钮
            'button_hover': (100, 149, 237), # 浅蓝色悬停
            'button_text': (255, 255, 255),  # 白色文字
            'status': (144, 238, 144)        # 浅绿状态
        }
    
    def clear_screen(self):
        """清空屏幕"""
        self.img.clear(color=self.colors['background'])
    
    def draw_title(self, title="K230 程序管理器"):
        """绘制标题栏"""
        title_x = (self.width - len(title) * 16) // 2  # 居中对齐
        self.img.draw_string_advanced(title_x, 15, 32, title, 
                                    color=self.colors['title'])
        
        # 绘制标题下划线
        line_y = TITLE_HEIGHT - 10
        self.img.draw_line(50, line_y, self.width - 50, line_y, 
                          color=self.colors['title'], thickness=2)
    
    def draw_program_button(self, index, program, is_selected=False):
        """绘制程序按钮"""
        # 计算按钮位置 (2列布局)
        col = index % 2
        row = index // 2
        
        x = 100 + col * (BUTTON_WIDTH + BUTTON_MARGIN)
        y = TITLE_HEIGHT + 40 + row * (BUTTON_HEIGHT + BUTTON_MARGIN)
        
        # 选择按钮颜色
        button_color = self.colors['button_hover'] if is_selected else self.colors['button_normal']
        
        # 绘制按钮背景
        self.img.draw_rectangle(x, y, BUTTON_WIDTH, BUTTON_HEIGHT, 
                              color=button_color, thickness=-1, fill=True)
        
        # 绘制按钮边框
        border_color = (255, 255, 255) if is_selected else (200, 200, 200)
        self.img.draw_rectangle(x, y, BUTTON_WIDTH, BUTTON_HEIGHT, 
                              color=border_color, thickness=2)
        
        # 绘制图标
        icon_x = x + 20
        icon_y = y + 15
        self.img.draw_string_advanced(icon_x, icon_y, 24, program['icon'], 
                                    color=self.colors['button_text'])
        
        # 绘制程序名称
        text_x = x + 60
        text_y = y + 20
        self.img.draw_string_advanced(text_x, text_y, 20, program['name'], 
                                    color=self.colors['button_text'])
        
        # 绘制程序描述
        desc_x = x + 10
        desc_y = y + 50
        self.img.draw_string_advanced(desc_x, desc_y, 14, program.get('desc', ''), 
                                    color=(200, 200, 200))
        
        return {'x': x, 'y': y, 'width': BUTTON_WIDTH, 'height': BUTTON_HEIGHT}
    
    def draw_status_bar(self, status_text="就绪"):
        """绘制状态栏"""
        status_y = self.height - 30
        self.img.draw_string_advanced(20, status_y, 16, f"状态: {status_text}", 
                                    color=self.colors['status'])
        
        # 绘制简单时间信息（避免localtime在某些环境下的问题）
        try:
            current_time = time.localtime()
            time_str = f"{current_time[3]:02d}:{current_time[4]:02d}:{current_time[5]:02d}"
            time_x = self.width - 100
            self.img.draw_string_advanced(time_x, status_y, 16, time_str,
                                        color=self.colors['status'])
        except:
            # 如果时间获取失败，显示简单信息
            self.img.draw_string_advanced(self.width - 80, status_y, 16, "K230",
                                        color=self.colors['status'])
    
    def draw_menu(self, programs, selected_index=-1, status="就绪"):
        """绘制完整菜单界面"""
        self.clear_screen()
        self.draw_title()
        
        # 绘制程序按钮并记录位置
        button_rects = []
        for i, program in enumerate(programs):
            is_selected = (i == selected_index)
            rect = self.draw_program_button(i, program, is_selected)
            button_rects.append(rect)
        
        self.draw_status_bar(status)
        return button_rects
    
    def show(self):
        """显示渲染结果"""
        Display.show_image(self.img)

class ProgramManager:
    """程序管理类"""
    
    def __init__(self):
        """初始化程序管理器"""
        self.programs = self.load_programs()
        self.current_program = None
    
    def load_programs(self):
        """加载可用程序列表"""
        programs = [
            {
                'name': '矩形检测',
                'file': '23E_XIFENG.py',
                'icon': '🔍',
                'desc': '米醋电控板矩形检测'
            },
            {
                'name': '颜色跟踪',
                'file': 'color_track.py', 
                'icon': '🎨',
                'desc': '颜色识别与跟踪'
            },
            {
                'name': 'AprilTag',
                'file': 'apriltag_detect.py',
                'icon': '🏷️',
                'desc': 'AprilTag标签识别'
            },
            {
                'name': '系统设置',
                'file': 'settings.py',
                'icon': '⚙️',
                'desc': '系统参数配置'
            },
            {
                'name': '退出菜单',
                'file': None,
                'icon': '🚪',
                'desc': '退出程序管理器'
            }
        ]
        
        # 检查程序文件是否存在
        available_programs = []
        for program in programs:
            if program['file'] is None or self.check_file_exists(program['file']):
                available_programs.append(program)
            else:
                print(f"⚠️ 程序文件不存在: {program['file']}")
        
        return available_programs
    
    def check_file_exists(self, filename):
        """检查文件是否存在 - MicroPython兼容版本"""
        try:
            with open(filename, 'r'):
                return True
        except:
            return False
    
    def run_program(self, program_index):
        """运行指定程序"""
        if 0 <= program_index < len(self.programs):
            program = self.programs[program_index]
            
            if program['name'] == '退出菜单':
                return False  # 退出菜单
            
            if program['file']:
                try:
                    print(f"🚀 启动程序: {program['name']}")
                    self.current_program = program
                    
                    # 清理资源后运行程序
                    gc.collect()
                    exec(open(program['file']).read())
                    
                except Exception as e:
                    print(f"❌ 程序运行错误: {e}")
                    return True  # 返回菜单
            
        return True  # 继续显示菜单

class TouchMenu:
    """触摸菜单主类"""
    
    def __init__(self):
        """初始化触摸菜单系统"""
        self.touch_handler = TouchHandler()
        self.renderer = MenuRenderer()
        self.program_manager = ProgramManager()
        self.selected_index = -1
        self.button_rects = []
        self.running = True
        
        # 初始化显示系统
        self.init_display()
    
    def init_display(self):
        """初始化显示系统"""
        try:
            # 初始化LCD显示 (ST7701控制器，800x480分辨率)
            Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
            MediaManager.init()
            print("✅ 显示系统初始化成功")
        except Exception as e:
            print(f"❌ 显示系统初始化失败: {e}")
            raise
    
    def check_button_touch(self, touch_x, touch_y):
        """检查触摸点是否在按钮区域内"""
        for i, rect in enumerate(self.button_rects):
            if (rect['x'] <= touch_x <= rect['x'] + rect['width'] and
                rect['y'] <= touch_y <= rect['y'] + rect['height']):
                return i
        return -1
    
    def run(self):
        """运行触摸菜单主循环"""
        print("🎉 K230触摸菜单系统启动")
        
        try:
            while self.running:
                os.exitpoint()
                
                # 渲染菜单界面
                self.button_rects = self.renderer.draw_menu(
                    self.program_manager.programs, 
                    self.selected_index,
                    "触摸选择程序"
                )
                self.renderer.show()
                
                # 处理触摸事件
                touch_event = self.touch_handler.get_touch_event()
                if touch_event:
                    touch_x, touch_y = touch_event['x'], touch_event['y']
                    print(f"👆 触摸位置: ({touch_x}, {touch_y})")
                    
                    # 检查按钮点击
                    button_index = self.check_button_touch(touch_x, touch_y)
                    if button_index >= 0:
                        self.selected_index = button_index
                        print(f"🎯 选择程序: {self.program_manager.programs[button_index]['name']}")
                        
                        # 短暂高亮显示
                        self.button_rects = self.renderer.draw_menu(
                            self.program_manager.programs, 
                            self.selected_index,
                            f"启动: {self.program_manager.programs[button_index]['name']}"
                        )
                        self.renderer.show()
                        time.sleep_ms(500)
                        
                        # 运行选中的程序
                        continue_menu = self.program_manager.run_program(button_index)
                        if not continue_menu:
                            self.running = False
                        
                        self.selected_index = -1  # 重置选择
                
                time.sleep_ms(50)  # 减少CPU占用
                gc.collect()  # 垃圾回收
                
        except KeyboardInterrupt:
            print("👋 用户退出菜单")
        except Exception as e:
            print(f"❌ 菜单运行错误: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            Display.deinit()
            MediaManager.deinit()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理错误: {e}")

def main():
    """主函数"""
    os.exitpoint(os.EXITPOINT_ENABLE)
    
    try:
        # 创建并运行触摸菜单
        menu = TouchMenu()
        menu.run()
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        import sys
        sys.print_exception(e)

if __name__ == "__main__":
    main()
