# 立创·庐山派-K230-CanMV开发板 - 矩形和激光检测系统
# 基于K230图像识别技术改写
import time, os, sys, math
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA

# 初始化摄像头和显示
picture_width = 640
picture_height = 480
sensor_id = 2
sensor = None

# UART2全局变量
serial = None
uart = None
fpioa = None

def init_uart2():
    """安全初始化UART2，包含冲突检测"""
    global serial, uart, fpioa

    try:
        # 检查UART2是否已经被占用
        if serial is not None:
            print("⚠️ UART2已经初始化，跳过重复初始化")
            return True

        print("🔧 配置UART2引脚复用...")
        fpioa = FPIOA()
        fpioa.set_function(11, FPIOA.UART2_TXD)  # GPIO11 -> UART2发送
        fpioa.set_function(12, FPIOA.UART2_RXD)  # GPIO12 -> UART2接收
        print("✅ FPIOA配置完成")

        # 初始化串口 - K230使用machine.UART
        serial = uart = UART(UART.UART2, baudrate=115200, bits=UART.EIGHTBITS, parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE, timeout=300)
        print("✅ UART2初始化完成")
        return True

    except Exception as e:
        print(f"❌ UART2初始化失败: {e}")
        return False

def cleanup_uart2():
    """清理UART2资源"""
    global serial, uart, fpioa

    try:
        if serial is not None:
            print("🧹 清理UART2资源...")
            serial.close()
            serial = None
            uart = None
            print("✅ UART2资源清理完成")
    except Exception as e:
        print(f"⚠️ UART2清理异常: {e}")
        serial = None
        uart = None





# 改进的K230摄像头初始化函数
def init_k230_camera():
    """初始化K230摄像头系统"""
    global sensor
    try:
        print("🔧 开始摄像头初始化...")

        # 构造摄像头对象
        sensor = Sensor(id=sensor_id)
        print("  ↳ 创建Sensor对象完成")

        # 重置传感器
        sensor.reset()
        print("  ↳ 传感器重置完成")

        # 设置输出尺寸和格式
        sensor.set_framesize(width=800, height=480, chn=CAM_CHN_ID_0)
        sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
        print(f"  ↳ 图像分辨率设置: {picture_width}x{picture_height}")
        print("  ↳ 像素格式设置: RGB565")

        print("✅ 摄像头初始化成功")
        return True
    except Exception as e:
        print(f"❌ 摄像头初始化失败: {e}")
        return False

def start_k230_sensor():
    """启动K230传感器"""
    global sensor
    try:
        print("🔧 启动传感器...")
        sensor.run()
        print("✅ 传感器启动成功")
        return True
    except Exception as e:
        print(f"❌ 传感器启动失败: {e}")
        return False

# 完整的K230系统初始化函数 - 修复LCD显示问题
def init_k230_system():
    """完整的K230系统初始化 - 分步骤初始化以确保LCD正常显示"""
    try:
        print("🚀 开始K230系统完整初始化...")

        # 步骤1: 初始化摄像头
        if not init_k230_camera():
            return False

        # 步骤2: 初始化LCD显示系统
        if not init_k230_display():
            return False

        # 步骤3: 初始化媒体管理器
        if not init_k230_media_manager():
            return False

        # 步骤4: 启动传感器
        if not start_k230_sensor():
            return False

        print("🎉 K230系统完整初始化成功")
        print("  ↳ 摄像头: ✅ 已就绪")
        print("  ↳ LCD显示: ✅ 已就绪 (ST7701 800x480)")
        print("  ↳ 媒体管理器: ✅ 已就绪")
        print("  ↳ 传感器: ✅ 已启动")
        return True

    except Exception as e:
        print(f"❌ K230系统初始化失败: {e}")
        return False

# 重复的函数定义已删除

# 定义通信协议相关常量
# 注意：已修改为STM32兼容格式
# 激光数据格式: red:(x,y)\n 和 gre:(x,y)\n
FRAME_HEADER = '$'  # 仅用于矩形数据（STM32端可能不处理）
FRAME_FOOTER = '\n'
RED_LASER_ID = 'R'  # 内部标识，发送时转换为"red:"
GREEN_LASER_ID = 'G'  # 内部标识，发送时转换为"gre:"
RECT_ID = 'M'  # M表示矩形(Middle Rectangle)坐标（STM32端可能不处理）

# 定义矩形过滤参数
min_area = 500  # 最小矩形面积（K230调整）
max_area = 200000  # 最大矩形面积（支持近距离大矩形）
min_aspect_ratio = 0.2  # 最小长宽比
max_aspect_ratio = 5  # 最大长宽比

# 性能优化参数（基于Marcus架构设计）
OPTIMIZED_THRESHOLD = 2500  # 优化后的threshold，接近官方推荐10000
ROI_MARGIN = 50  # 自适应ROI的边距
DEFAULT_ROI_SCALE = 0.8  # 默认ROI占图像的比例（中心80%区域）

# ROI区域定义（性能优化关键）
def get_default_roi():
    """获取默认的ROI区域（图像中心区域）"""
    roi_w = int(picture_width * DEFAULT_ROI_SCALE)
    roi_h = int(picture_height * DEFAULT_ROI_SCALE)
    roi_x = (picture_width - roi_w) // 2
    roi_y = (picture_height - roi_h) // 2
    return (roi_x, roi_y, roi_w, roi_h)

# 新增变量用于记录内外框检测结果
rect_detection_results = []
DETECTION_TIMES = 5  # 减少检测次数以提高响应速度
middle_rect_points = []  # 中间矩形的点
SERIAL_POLL_INTERVAL_MS = 50  # 串口发送轮询间隔，单位毫秒
last_send_time = 0  # 上次发送时间（共用一个时间戳）
send_red_next = True  # 下次发送红色激光坐标标志
rect_sent = False  # 矩形坐标是否已发送标志

# 自适应ROI优化变量
previous_rects = None  # 上一帧的矩形检测结果
adaptive_roi = None  # 自适应ROI区域

# 颜色阈值定义（LAB颜色空间）
red_laser_threshold = [(20, 80, 20, 70, 10, 60)]    # 红色激光阈值
green_laser_threshold = [(30, 80, -60, -20, 10, 50)]  # 绿色激光阈值

# 发送激光坐标数据函数（K230版本 - 适配STM32格式）
def send_laser_coordinates(laser_type, coords):
    if coords is None:
        return

    x, y = coords

    # 根据STM32期望格式构建数据
    # STM32期望格式: red:(x,y)\n 或 gre:(x,y)\n
    if laser_type == RED_LASER_ID:  # 'R'
        data = f"red:({x},{y})\n"
    elif laser_type == GREEN_LASER_ID:  # 'G'
        data = f"gre:({x},{y})\n"
    else:
        print(f"未知激光类型: {laser_type}")
        return

    # 通过串口发送数据（STM32兼容格式）
    try:
        serial.write(data.encode())
        print(f"串口发送: {data.strip()}")
    except Exception as e:
        print(f"串口发送失败: {e}")

# 发送矩形坐标数据函数（K230版本 - 修改为A-I字母格式）
def send_rectangle_coordinates(rect_points):
    if rect_points is None or len(rect_points) == 0:
        return

    # 定义坐标点标识符（按顺序对应8个点）
    point_labels = ['A', 'B', 'C', 'D', 'E', 'F', 'H', 'I']

    # 确保有8个点
    if len(rect_points) != 8:
        print(f"⚠️ 矩形坐标点数量不正确: {len(rect_points)}，期望8个点")
        return

    # 逐个发送坐标点，格式：A:(x,y)

    try:
        for i, (x, y) in enumerate(rect_points):
            if i < len(point_labels):
                data = f"{point_labels[i]}:({x},{y})\n"
                serial.write(data.encode())
                print(f"串口发送: {data.strip()}")

        print("✅ 矩形坐标发送完成 (A-I格式)")

    except Exception as e:
        print(f"串口发送失败: {e}")

# K230矩形检测函数（性能优化版本 - 基于Marcus架构）
def detect_rectangles_k230_optimized(img):
    """使用优化的find_rects函数检测矩形 - 四层优化方案"""
    global previous_rects, adaptive_roi

    try:
        # 方案1: 参数优化 - 提高threshold减少候选矩形
        # 方案2: ROI区域限制 - 智能选择检测区域
        current_roi = get_adaptive_roi()

        # 使用优化参数进行矩形检测
        rectangles = img.find_rects(roi=current_roi, threshold=OPTIMIZED_THRESHOLD)

        # 方案3: 预过滤优化 - 简化过滤逻辑，减少后处理
        filtered_rects = []
        for rect in rectangles:
            area = rect.w() * rect.h()
            # 简化过滤条件，只保留核心面积过滤
            if min_area < area < max_area:
                filtered_rects.append(rect)

        # 直接限制数量，避免复杂排序
        if len(filtered_rects) > 2:
            # 按面积快速排序，只取前2个
            filtered_rects.sort(key=lambda r: r.w() * r.h(), reverse=True)
            filtered_rects = filtered_rects[:2]

        # 方案4: 自适应检测 - 更新previous_rects用于下次优化
        if len(filtered_rects) >= 2:
            previous_rects = filtered_rects

        return filtered_rects

    except Exception as e:
        print(f"矩形检测错误: {e}")
        return []

def get_adaptive_roi():
    """获取自适应ROI区域 - 智能性能优化"""
    global previous_rects, adaptive_roi

    if previous_rects and len(previous_rects) >= 2:
        # 基于上次检测结果缩小ROI范围（方案4：自适应检测）
        outer_rect = previous_rects[0]  # 面积最大的矩形

        # 计算自适应ROI，在上次结果周围扩展margin
        roi_x = max(0, outer_rect.x() - ROI_MARGIN)
        roi_y = max(0, outer_rect.y() - ROI_MARGIN)
        roi_w = min(picture_width - roi_x, outer_rect.w() + 2 * ROI_MARGIN)
        roi_h = min(picture_height - roi_y, outer_rect.h() + 2 * ROI_MARGIN)

        adaptive_roi = (roi_x, roi_y, roi_w, roi_h)
        return adaptive_roi
    else:
        # 使用默认ROI（图像中心区域）
        return get_default_roi()

# 保留原函数作为备用（向后兼容）
def detect_rectangles_k230(img):
    """原始矩形检测函数（备用）"""
    return detect_rectangles_k230_optimized(img)

# 检查位置稳定性（K230版本）
def is_position_stable_k230(results):
    """检查检测结果的位置稳定性"""
    if len(results) < DETECTION_TIMES:
        return False

    # 检查最近几次检测的位置变化
    if len(results) >= 2:
        last_result = results[-1]
        prev_result = results[-2]

        if len(last_result) == 2 and len(prev_result) == 2:
            # 比较两个矩形的中心点位置
            for i in range(2):
                last_rect = last_result[i]
                prev_rect = prev_result[i]

                last_center_x = last_rect.x() + last_rect.w() // 2
                last_center_y = last_rect.y() + last_rect.h() // 2
                prev_center_x = prev_rect.x() + prev_rect.w() // 2
                prev_center_y = prev_rect.y() + prev_rect.h() // 2

                # 计算中心点距离
                distance = math.sqrt((last_center_x - prev_center_x)**2 +
                                   (last_center_y - prev_center_y)**2)

                if distance > 20:  # 位置变化阈值
                    return False

            return True

    return False

# K230版本的绘制中间矩形函数
def draw_middle_rect_k230(outer_rect, inner_rect, img):
    """绘制两个矩形之间的中间矩形（K230版本）"""
    global middle_rect_points

    try:
        # 获取外框和内框的角点
        outer_corners = outer_rect.corners()
        inner_corners = inner_rect.corners()

        # 计算中间点
        middle_points = []
        for i in range(4):
            mid_x = (outer_corners[i][0] + inner_corners[i][0]) // 2
            mid_y = (outer_corners[i][1] + inner_corners[i][1]) // 2
            middle_points.append((mid_x, mid_y))

        # 计算边的中点
        for i in range(4):
            next_i = (i + 1) % 4
            mid_edge_x = (middle_points[i][0] + middle_points[next_i][0]) // 2
            mid_edge_y = (middle_points[i][1] + middle_points[next_i][1]) // 2
            middle_points.append((mid_edge_x, mid_edge_y))

        middle_rect_points = middle_points

        # 绘制中间矩形的边
        for i in range(4):
            next_i = (i + 1) % 4
            img.draw_line(middle_points[i][0], middle_points[i][1],
                         middle_points[next_i][0], middle_points[next_i][1],
                         color=(255, 0, 0), thickness=2)

        # 绘制所有点
        for point in middle_points:
            img.draw_circle(point[0], point[1], 5, color=(255, 0, 0), fill=True)

        return img

    except Exception as e:
        print(f"绘制中间矩形错误: {e}")
        return img

# K230版本的激光检测函数
def detect_lasers_k230(img):
    """使用K230的颜色检测功能检测红色和绿色激光点"""
    brightest_red_coords = None
    brightest_green_coords = None

    try:
        # 检测红色激光点
        red_blobs = img.find_blobs(
            red_laser_threshold,
            area_threshold=5,     # 最小面积
            pixels_threshold=3,   # 最小像素数
            merge=True,          # 合并重叠区域
            margin=3             # 合并边距
        )

        if red_blobs:
            # 找到面积最大的红色激光点
            largest_red_blob = max(red_blobs, key=lambda b: b.area())
            brightest_red_coords = (largest_red_blob.cx(), largest_red_blob.cy())

            # 绘制红色激光点
            img.draw_circle(brightest_red_coords[0], brightest_red_coords[1],
                           5, color=(255, 0, 0), fill=True)
            img.draw_string_advanced(brightest_red_coords[0], brightest_red_coords[1] - 15,
                                   12, "Red Laser", color=(255, 0, 0))

        # 检测绿色激光点
        green_blobs = img.find_blobs(
            green_laser_threshold,
            area_threshold=5,     # 最小面积
            pixels_threshold=3,   # 最小像素数
            merge=True,          # 合并重叠区域
            margin=3             # 合并边距
        )

        if green_blobs:
            # 找到面积最大的绿色激光点
            largest_green_blob = max(green_blobs, key=lambda b: b.area())
            brightest_green_coords = (largest_green_blob.cx(), largest_green_blob.cy())

            # 绘制绿色激光点
            img.draw_circle(brightest_green_coords[0], brightest_green_coords[1],
                           5, color=(0, 255, 0), fill=True)
            img.draw_string_advanced(brightest_green_coords[0], brightest_green_coords[1] - 15,
                                   12, "Green Laser", color=(0, 255, 0))

        return brightest_red_coords, brightest_green_coords

    except Exception as e:
        print(f"激光检测错误: {e}")
        return None, None

# 改进的K230系统初始化函数 - 修复LCD显示问题
def init_k230_display():
    """初始化K230 LCD显示系统 - 基于正确的初始化顺序"""
    try:
        print("📺 开始LCD显示系统初始化...")

        # 步骤1: 初始化Display (ST7701控制器，800x480分辨率)
        print("  ↳ 初始化ST7701 LCD控制器...")
        Display.init(Display.ST7701, width=800, height=480, to_ide=True)
        print("  ↳ LCD控制器初始化成功")
        print(f"  ↳ 显示分辨率: 800x480")
        print("  ↳ 控制器类型: ST7701 (CanMV K230官方)")

        return True

    except Exception as e:
        print(f"❌ LCD显示系统初始化失败: {e}")
        return False

def init_k230_media_manager():
    """初始化K230媒体管理器"""
    try:
        print("🔧 初始化媒体管理器...")
        MediaManager.init()
        print("✅ 媒体管理器初始化成功")
        return True

    except Exception as e:
        print(f"❌ 媒体管理器初始化失败: {e}")
        return False

# K230主程序
def main():
    global rect_detection_finished, rect_sent, last_send_time, send_red_next

    # 初始化UART2通信
    if not init_uart2():
        print("UART2初始化失败，退出程序")
        return

    # 初始化K230系统
    if not init_k230_system():
        print("系统初始化失败，退出程序")
        cleanup_uart2()
        return

    print("🎉 K230系统初始化完成，开始主程序...")

    rect_detection_finished = False


    try:
        fps = time.clock()

        while True:
            fps.tick()
            os.exitpoint()

            # 获取图像
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            current_time_ms = time.ticks_ms()

            # 矩形检测阶段（使用性能优化版本）
            if not rect_detection_finished:
                merged_rectangles = detect_rectangles_k230_optimized(img)

                if len(merged_rectangles) == 2:
                    rect_detection_results.append(merged_rectangles)

                    if is_position_stable_k230(rect_detection_results):
                        outer_rect = merged_rectangles[0]
                        inner_rect = merged_rectangles[1]
                        img = draw_middle_rect_k230(outer_rect, inner_rect, img)
                        print("中间矩形点:", middle_rect_points)
                        rect_detection_finished = True

                        # 发送矩形坐标数据（仅发送一次）
                        if not rect_sent:
                            send_rectangle_coordinates(middle_rect_points)
                            rect_sent = True
                    else:
                        # 绘制检测中的矩形
                        for i, rect in enumerate(merged_rectangles):
                            color = (255, 0, 0) if i == 0 else (0, 255, 0)
                            img.draw_rectangle(rect.rect(), color=color, thickness=2)
                            label = "外框" if i == 0 else "内框"
                            x, y, w, h = rect.rect()
                            img.draw_string_advanced(x, y-20, 14, label, color=color)

                elif len(merged_rectangles) == 1:
                    rect = merged_rectangles[0]
                    area = rect.w() * rect.h()
                    if area > (max_area + min_area) / 2:
                        color = (255, 0, 0)
                        label = "外框"
                    else:
                        color = (0, 255, 0)
                        label = "内框"
                    img.draw_rectangle(rect.rect(), color=color, thickness=2)
                    x, y, w, h = rect.rect()
                    img.draw_string_advanced(x, y-20, 14, label, color=color)
            else:
                # 绘制中间矩形的点
                for point in middle_rect_points:
                    img.draw_circle(point[0], point[1], 5, color=(255, 0, 0), fill=True)

                # 检测激光点（只在矩形检测完成后执行）
                red_coords, green_coords = detect_lasers_k230(img)

                # 根据时间间隔轮询发送激光坐标
                if current_time_ms - last_send_time >= SERIAL_POLL_INTERVAL_MS:
                    if send_red_next:
                        if red_coords:
                            print("红色激光坐标:", red_coords)
                            send_laser_coordinates(RED_LASER_ID, red_coords)
                            last_send_time = current_time_ms
                            send_red_next = False
                        elif green_coords:
                            print("绿色激光坐标:", green_coords)
                            send_laser_coordinates(GREEN_LASER_ID, green_coords)
                            last_send_time = current_time_ms
                            send_red_next = True
                    else:
                        if green_coords:
                            print("绿色激光坐标:", green_coords)
                            send_laser_coordinates(GREEN_LASER_ID, green_coords)
                            last_send_time = current_time_ms
                            send_red_next = True
                        elif red_coords:
                            print("红色激光坐标:", red_coords)
                            send_laser_coordinates(RED_LASER_ID, red_coords)
                            last_send_time = current_time_ms
                            send_red_next = False

            # 显示状态信息（增加性能优化监控）
            img.draw_string_advanced(10, 10, 16, f"矩形检测: {'完成' if rect_detection_finished else '进行中'}",
                                   color=(255, 255, 255))
            img.draw_string_advanced(10, 30, 16, f"FPS: {fps.fps():.1f}", color=(255, 255, 0))

            # 性能优化信息显示
            if adaptive_roi:
                img.draw_string_advanced(10, 50, 16, f"ROI: {adaptive_roi[2]}x{adaptive_roi[3]}", color=(0, 255, 255))
            img.draw_string_advanced(10, 70, 16, f"优化: threshold={OPTIMIZED_THRESHOLD}", color=(0, 255, 255))

            # 显示图像
            Display.show_image(img)

    except KeyboardInterrupt:
        print("用户停止程序")
    except Exception as e:
        print(f"程序运行错误: {e}")
    finally:
        # 改进的资源清理 - 确保LCD正确关闭
        print("\n🧹 开始资源清理...")
        try:
            if sensor:
                print("  ↳ 停止传感器...")
                sensor.stop()
                print("  ↳ 传感器已停止")

            print("  ↳ 关闭LCD显示...")
            Display.deinit()
            print("  ↳ LCD显示已关闭")

            print("  ↳ 释放媒体管理器...")
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("  ↳ 媒体管理器已释放")

            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️  清理异常: {e}")

        # 清理UART2资源
        cleanup_uart2()

        print("👋 K230矩形和激光检测系统已退出")

# 运行主程序
if __name__ == "__main__":
    main()
