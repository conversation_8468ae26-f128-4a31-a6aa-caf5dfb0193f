# K230 触摸菜单直接启动器
# 运行后直接进入触摸菜单界面，无文本选择

import os
import gc

def main():
    """主函数 - 直接启动触摸菜单"""
    
    # 显示启动信息
    print("🎉 K230 触摸程序管理器")
    print("=" * 50)
    print("📱 支持触摸LCD操作")
    print("🔄 支持程序一键切换")
    print("⚙️ MicroPython优化版")
    print("=" * 50)
    
    try:
        # 切换到sdcard目录
        current_dir = os.getcwd()
        if '/sdcard' not in current_dir:
            print("🔄 切换到程序目录...")
            os.chdir('/sdcard')
            print(f"✅ 工作目录: {os.getcwd()}")
        
        # 检查触摸菜单文件
        print("🎯 尝试启动触摸菜单...")
        
        try:
            with open('touch_menu.py', 'r') as f:
                pass
            print("✅ touch_menu.py 文件存在")
        except:
            print("❌ touch_menu.py 文件不存在")
            print("⚠️ 触摸菜单启动失败，使用简化菜单")
            return run_fallback_menu()
        
        # 启动触摸菜单
        print("🚀 启动触摸菜单系统...")
        
        try:
            # 导入触摸菜单模块
            from touch_menu import TouchMenu
            print("✅ 触摸菜单模块导入成功")
            
            # 创建并运行触摸菜单
            menu = TouchMenu()
            menu.run()
            
            print("✅ 触摸菜单运行完成")
            
        except ImportError as e:
            print(f"❌ 导入触摸菜单失败: {e}")
            print("💡 可能是硬件不支持或模块缺失")
            return run_fallback_menu()
            
        except Exception as e:
            print(f"❌ 触摸菜单运行错误: {e}")
            print("💡 可能是硬件不兼容")
            return run_fallback_menu()
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        import sys
        sys.print_exception(e)
        return run_fallback_menu()
    
    finally:
        print("🧹 清理系统资源...")
        gc.collect()
        print("✅ 程序管理器已退出")

def run_fallback_menu():
    """备用简化菜单"""
    print("🔄 启动简化菜单...")
    
    try:
        # 扫描程序文件
        all_files = os.listdir(".")
        python_files = [f for f in all_files if f.endswith('.py')]
        
        # 过滤系统文件
        system_files = ['main.py', 'main_touch_only.py', 'touch_menu.py']
        programs = [f for f in python_files if f not in system_files]
        
        if not programs:
            print("❌ 没有找到可用的程序文件")
            print("请确保程序文件存在于当前目录")
            return
        
        print("\n" + "=" * 40)
        print("📋 K230 程序选择菜单")
        print("=" * 40)
        
        # 程序友好名称
        program_names = {
            '23E_Xifeng.py': '🔍 矩形检测程序',
            'color_track.py': '🎨 颜色跟踪程序',
            'apriltag_detect.py': '🏷️ AprilTag识别'
        }
        
        while True:
            # 显示程序列表
            for i, filename in enumerate(programs):
                name = program_names.get(filename, f"📄 {filename}")
                print(f"{i+1}. {name}")
            
            print("0. 退出")
            print("=" * 40)
            
            try:
                choice = input("请选择程序 (0-{}): ".format(len(programs)))
                
                if choice == '0':
                    print("👋 退出程序管理器")
                    break
                elif choice.isdigit() and 1 <= int(choice) <= len(programs):
                    selected_file = programs[int(choice) - 1]
                    
                    print(f"🚀 启动程序: {selected_file}")
                    
                    try:
                        with open(selected_file, 'r') as f:
                            program_code = f.read()
                        
                        print("📝 程序代码读取成功")
                        print("⚡ 开始执行程序...")
                        print("=" * 50)
                        
                        exec(program_code)
                        
                        print("=" * 50)
                        print("✅ 程序执行完成")
                        
                    except Exception as e:
                        print(f"❌ 程序执行错误: {e}")
                        import sys
                        sys.print_exception(e)
                    
                    input("按回车键继续...")
                    
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 用户退出")
                break
            except Exception as e:
                print(f"❌ 菜单错误: {e}")
        
    except Exception as e:
        print(f"❌ 简化菜单启动失败: {e}")

if __name__ == "__main__":
    main()
